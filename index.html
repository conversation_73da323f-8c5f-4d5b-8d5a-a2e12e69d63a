<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>E-Cell APSIT - Ignite & Innovate</title>

    <script src="https://cdn.tailwindcss.com"></script>

    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link
        href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700;800&family=Poppins:wght@300;400;600;700&display=swap"
        rel="stylesheet">
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background-color: #0f172a;
            /* Slate 900 */
        }

        h1,
        h2,
        h3 {
            font-family: 'Montserrat', sans-serif;
        }

        html {
            scroll-behavior: smooth;
        }

        .header-scrolled {
            background-color: rgba(15, 23, 42, 0.9);
            backdrop-filter: blur(8px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease-in-out;
        }

        .neon-button {
            position: relative;
            background: linear-gradient(45deg, #8b5cf6, #ec4899);
            padding: 12px 32px;
            border-radius: 9999px;
            color: white;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.1em;
            overflow: hidden;
            transition: all 0.3s ease-in-out;
            border: none;
            cursor: pointer;
        }

        .neon-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .neon-button:before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: rgba(255, 255, 255, 0.1);
            transform: rotate(30deg);
            transition: all 0.3s ease-in-out;
        }

        .neon-button:hover:not(:disabled):before {
            transform: rotate(0deg);
            opacity: 0;
        }

        .neon-button:hover:not(:disabled) {
            transform: scale(1.05);
            box-shadow: 0 0 20px #a855f7, 0 0 40px #ec4899;
        }

        .card {
            background-color: #1e293b;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.4);
        }

        .hero-bg {
            background: #0f172a;
            background-image: linear-gradient(to bottom, rgba(25, 33, 50, 0.7), rgba(15, 23, 42, 1)), url(https://placehold.co/1920x1080/0f172a/0f172a?text=);
            background-size: cover;
            background-position: center;
            position: relative;
        }

        .hero-bg:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 10% 20%, rgba(139, 92, 246, 0.1) 0%, transparent 50%), radial-gradient(circle at 90% 80%, rgba(236, 72, 153, 0.1) 0%, transparent 50%);
        }

        .spinner {
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top: 4px solid #fff;
            width: 24px;
            height: 24px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        /* Modal Styles */
        .modal {
            display: none;
            /* Hidden by default */
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0, 0, 0, 0.7);
            animation: fadeIn 0.3s;
        }

        .modal-content {
            background-color: #1e293b;
            margin: 10% auto;
            padding: 2rem;
            border: 1px solid #888;
            width: 90%;
            max-width: 500px;
            border-radius: 1rem;
            position: relative;
            animation: slideIn 0.4s;
        }

        .close-button {
            color: #aaa;
            position: absolute;
            top: 1rem;
            right: 1.5rem;
            font-size: 2rem;
            font-weight: bold;
            transition: color 0.2s;
        }

        .close-button:hover,
        .close-button:focus {
            color: #fff;
            text-decoration: none;
            cursor: pointer;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
            }

            to {
                opacity: 1;
            }
        }

        @keyframes slideIn {
            from {
                transform: translateY(-50px);
                opacity: 0;
            }

            to {
                transform: translateY(0);
                opacity: 1;
            }
        }
    </style>
</head>

<body class="text-gray-200">

    <header id="header" class="fixed top-0 left-0 w-full z-50 transition-all duration-300 py-4">
        <div class="container mx-auto px-4 flex justify-between items-center">

            <a href="/" aria-label="E-Cell APSIT Homepage" class="flex items-center space-x-4">
                <img src="assets\Ecelllg.png" alt="E-Cell APSIT Logo" class="h-24 w-auto">
                <span class="text-2xl font-bold text-gray-400">Ecell APSIT</span>
            </a>

            <nav class="hidden md:flex space-x-8 text-gray-400 font-medium">
                <a href="#about" class="hover:text-purple-400 transition-colors">About</a>
                <a href="#toolkit" class="hover:text-purple-400 transition-colors">Toolkit</a>
                <a href="#team" class="hover:text-purple-400 transition-colors">Team</a>
                <a href="#events" class="hover:text-purple-400 transition-colors">Events</a>
                <a href="#contact" class="hover:text-purple-400 transition-colors">Contact</a>
            </nav>

            <button id="mobile-menu-button" class="md:hidden text-gray-300 focus:outline-none">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16m-7 6h7" />
                </svg>
            </button>

        </div>
    </header>


    <div id="mobile-menu"
        class="fixed top-0 left-0 w-full h-full bg-slate-900 z-40 transform -translate-x-full transition-transform duration-300 md:hidden p-8">
        <div class="flex justify-end">
            <button id="close-menu-button" class="text-gray-300">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
        </div>
        <nav class="mt-8 flex flex-col space-y-6 text-2xl font-bold text-center">
            <a href="#about" class="py-2 text-white hover:text-purple-400 transition-colors">About</a>
            <a href="#toolkit" class="py-2 text-white hover:text-purple-400 transition-colors">Toolkit</a>
            <a href="#team" class="py-2 text-white hover:text-purple-400 transition-colors">Team</a>
            <a href="#events" class="py-2 text-white hover:text-purple-400 transition-colors">Events</a>
            <a href="#contact" class="py-2 text-white hover:text-purple-400 transition-colors">Contact</a>
        </nav>
    </div>

    <main>
        <section id="hero"
            class="hero-bg text-white min-h-screen flex items-center justify-center p-8 relative overflow-hidden">
            <div class="container mx-auto text-center relative z-10">
                <h1 class="text-4xl sm:text-5xl lg:text-7xl font-extrabold tracking-tight mb-4 animate-fade-in-up">
                    Igniting Ideas. Empowering Entrepreneurs.
                </h1>
                <p
                    class="text-lg sm:text-xl font-light mb-8 max-w-2xl mx-auto text-gray-300 animate-fade-in-up delay-200">
                    Your journey from idea to impact starts here.
                </p>
                <a href="#toolkit" class="neon-button shadow-lg animate-fade-in-up delay-400">
                    Explore Our Tools →
                </a>
            </div>
        </section>

        <section id="about" class="py-20 bg-slate-900">
            <div class="container mx-auto px-4 text-center">
                <h2 class="text-3xl sm:text-4xl font-bold mb-4 text-white">About E-Cell APSIT</h2>
                <p class="text-gray-400 max-w-3xl mx-auto text-lg leading-relaxed">
                    The Entrepreneurship Cell is a dynamic student-led initiative that fosters innovation, nurtures
                    entrepreneurial mindsets, and empowers individuals to transform ideas into impactful ventures that
                    shape tomorrow's world.
                </p>
                <div class="mt-12 grid gap-8 md:grid-cols-3">
                    <div class="card p-8 rounded-xl shadow-lg border-indigo-500/20">
                        <h3 class="text-xl font-bold mb-2 text-purple-400">Innovation Hub</h3>
                        <p class="text-gray-400">Foster creative thinking and breakthrough solutions.</p>
                    </div>
                    <div class="card p-8 rounded-xl shadow-lg border-purple-500/20">
                        <h3 class="text-xl font-bold mb-2 text-purple-400">Community</h3>
                        <p class="text-gray-400">Build networks with like-minded entrepreneurs.</p>
                    </div>
                    <div class="card p-8 rounded-xl shadow-lg border-pink-500/20">
                        <h3 class="text-xl font-bold mb-2 text-purple-400">Goal-Oriented</h3>
                        <p class="text-gray-400">Transform ideas into successful ventures.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Founder's Toolkit Section -->
        <section id="toolkit" class="py-20 bg-slate-800 text-white">
            <div class="container mx-auto px-4 text-center">
                <h2 class="text-3xl sm:text-4xl font-bold mb-4 text-white">Founder's Toolkit</h2>
                <p class="text-gray-400 max-w-3xl mx-auto text-lg leading-relaxed mb-12">
                    Powerful AI-driven tools to help you build, pitch, and grow your startup.
                </p>
                <div class="grid gap-12 lg:grid-cols-2">
                    <!-- Innovation Spark -->
                    <div class="card p-8 rounded-xl shadow-lg border-indigo-500/20">
                        <h3 class="text-2xl font-bold mb-4 text-white">Innovation Spark ✨</h3>
                        <p class="text-gray-400 mb-6">Stuck on an idea? Describe your interests or a problem you want to
                            solve, and we'll help you generate a startup concept.</p>
                        <textarea id="idea-input" rows="4"
                            class="w-full p-4 bg-slate-900 text-white rounded-lg border border-slate-700 focus:ring-purple-400 focus:border-purple-400 outline-none transition-colors"
                            placeholder="e.g., 'sustainable fashion for students'"></textarea>
                        <button id="generate-idea-button" class="neon-button mt-6">
                            Generate Startup Idea
                        </button>
                        <div id="idea-result"
                            class="mt-8 text-left bg-slate-900 p-6 rounded-lg border border-slate-700 hidden">
                            <p id="idea-loading"
                                class="text-gray-400 text-center flex items-center justify-center gap-2 hidden">
                                <span class="spinner"></span> Generating...
                            </p>
                            <div id="idea-content" class="text-gray-300 whitespace-pre-line"></div>
                        </div>
                    </div>

                    <!-- Pitch Deck Outline Generator -->
                    <div class="card p-8 rounded-xl shadow-lg border-pink-500/20">
                        <h3 class="text-2xl font-bold mb-4 text-white">Pitch Deck Generator ✨</h3>
                        <p class="text-gray-400 mb-6">Have an idea? Get a head start on your pitch with a professionally
                            structured 10-slide outline.</p>
                        <textarea id="pitch-input" rows="4"
                            class="w-full p-4 bg-slate-900 text-white rounded-lg border border-slate-700 focus:ring-purple-400 focus:border-purple-400 outline-none transition-colors"
                            placeholder="e.g., 'An app that connects local farmers to consumers'"></textarea>
                        <button id="generate-pitch-button" class="neon-button mt-6">
                            Generate Pitch Outline
                        </button>
                        <div id="pitch-result"
                            class="mt-8 text-left bg-slate-900 p-6 rounded-lg border border-slate-700 hidden">
                            <p id="pitch-loading"
                                class="text-gray-400 text-center flex items-center justify-center gap-2 hidden">
                                <span class="spinner"></span> Generating...
                            </p>
                            <div id="pitch-content" class="text-gray-300 whitespace-pre-line"></div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section id="impact" class="py-20 bg-slate-900">
            <div class="container mx-auto px-4 text-center">
                <h2 class="text-3xl sm:text-4xl font-bold mb-4 text-white">Our Impact</h2>
                <p class="text-gray-400 max-w-2xl mx-auto text-lg mb-12">
                    Numbers that speak for our community.
                </p>
                <div class="grid gap-8 md:grid-cols-4">
                    <div class="card p-6 rounded-xl shadow-lg">
                        <h3 class="text-4xl font-extrabold text-pink-400 mb-2" data-target="0">0</h3>
                        <p class="text-gray-400 font-medium">Startups Launched</p>
                    </div>
                    <div class="card p-6 rounded-xl shadow-lg">
                        <h3 class="text-4xl font-extrabold text-pink-400 mb-2" data-target="1">0</h3>
                        <p class="text-gray-400 font-medium">Events Conducted</p>
                    </div>
                    <div class="card p-6 rounded-xl shadow-lg">
                        <h3 class="text-4xl font-extrabold text-pink-400 mb-2" data-target="500">0</h3>
                        <p class="text-gray-400 font-medium">Active Members</p>
                    </div>
                    <div class="card p-6 rounded-xl shadow-lg">
                        <h3 class="text-4xl font-extrabold text-pink-400 mb-2" data-target="0">0</h3>
                        <p class="text-gray-400 font-medium">Success Rate</p>
                    </div>
                </div>
            </div>
        </section>

        <section id="team" class="py-20 bg-slate-800">
            <div class="container mx-auto px-4 text-center">
                <h2 class="text-3xl sm:text-4xl font-bold mb-4 text-white">Meet Our Team</h2>
                <p class="text-gray-400 max-w-2xl mx-auto text-lg mb-12">
                    Passionate leaders driving innovation and entrepreneurship at APSIT.
                </p>
                <div class="grid gap-8 sm:grid-cols-2 lg:grid-cols-4">
                    <div class="team-member-card card p-6 rounded-xl shadow-lg text-center group cursor-pointer"
                        data-name="Charvi Kashyap" data-title="President" data-img="assets\President.jpeg"
                        data-bio="As the President, Charvi leads the strategic vision of E-Cell, fostering a culture of innovation and empowering the next generation of entrepreneurs at APSIT.">
                        <img src="assets\President.jpeg" alt="Charvi Kashyap"
                            class="w-36 h-35 mx-auto rounded-full object-cover mb-4 ring-4 ring-pink-400 group-hover:ring-purple-400 transition-colors">
                        <h3 class="text-xl font-bold text-white">Charvi Kashyap</h3>
                        <p class="text-purple-400 font-medium mb-2">President</p>
                    </div>
                    <div class="team-member-card card p-6 rounded-xl shadow-lg text-center group cursor-pointer"
                        data-name="Surili Ghosh" data-title="Technical Head" data-img="assets\Technical.jpeg"
                        data-bio="Surili is the driving force behind our technical initiatives. She manages the E-Cell's digital infrastructure and leads workshops on cutting-edge technologies.">
                        <img src="assets\Technical.jpeg" alt="Surili Ghosh"
                            class="w-36 h-35 mx-auto rounded-full object-cover mb-4 ring-4 ring-purple-400 group-hover:ring-pink-400 transition-colors">
                        <h3 class="text-xl font-bold text-white">Surili Ghosh</h3>
                        <p class="text-pink-400 font-medium mb-2">Technical Head</p>
                    </div>
                    <div class="team-member-card card p-6 rounded-xl shadow-lg text-center group cursor-pointer"
                        data-name="Princess Jain" data-title="Marketing Head" data-img="assets/Marketing.jpeg"
                        data-bio="Princess crafts our narrative and amplifies our reach. Her strategic marketing campaigns ensure that E-Cell's events and initiatives make a significant impact.">
                        <img src="assets/Marketing.jpeg" alt="Princess Jain"
                            class="w-36 h-35 mx-auto rounded-full object-cover mb-4 ring-4 ring-pink-400 group-hover:ring-purple-400 transition-colors">
                        <h3 class="text-xl font-bold text-white">Princess Jain</h3>
                        <p class="text-purple-400 font-medium mb-2">Marketing Head</p>
                    </div>
                    <div class="team-member-card card p-6 rounded-xl shadow-lg text-center group cursor-pointer"
                        data-name="Prisha Jain" data-title="Operations Head" data-img="assets\Operations.jpeg"
                        data-bio="Prisha is the organizational backbone of the E-Cell. She ensures that all our events, workshops, and meetings run smoothly and efficiently.">
                        <img src="assets\Operations.jpeg" alt="Prisha Jain"
                            class="w-36 h-35 mx-auto rounded-full object-cover mb-4 ring-4 ring-purple-400 group-hover:ring-pink-400 transition-colors">
                        <h3 class="text-xl font-bold text-white">Prisha Jain</h3>
                        <p class="text-pink-400 font-medium mb-2">Operations Head</p>
                    </div>
                    <div class="team-member-card card p-6 rounded-xl shadow-lg text-center group cursor-pointer"
                        data-name="Siddhi Jedhe" data-title="Research Head" data-img="assets\Research.jpeg"
                        data-bio="Siddhi keeps the E-Cell at the forefront of industry trends. She leads market research and analysis to provide valuable insights for budding entrepreneurs.">
                        <img src="assets\Research.jpeg" alt="Siddhi Jedhe"
                            class="w-36 h-35 mx-auto rounded-full object-cover mb-4 ring-4 ring-pink-400 group-hover:ring-purple-400 transition-colors">
                        <h3 class="text-xl font-bold text-white">Siddhi Jedhe</h3>
                        <p class="text-purple-400 font-medium mb-2">Research Head</p>
                    </div>
                    <div class="team-member-card card p-6 rounded-xl shadow-lg text-center group cursor-pointer"
                        data-name="Aakansha Kannojia" data-title="Design Head" data-img="assets\Design.jpeg"
                        data-bio="Aakansha is our creative visionary. She is responsible for E-Cell's visual identity, creating compelling designs that captivate our audience.">
                        <img src="assets\Design.jpeg" alt="Aakansha Kannojia"
                            class="w-36 h-35 mx-auto rounded-full object-cover mb-4 ring-4 ring-purple-400 group-hover:ring-pink-400 transition-colors">
                        <h3 class="text-xl font-bold text-white">Aakansha Kannojia</h3>
                        <p class="text-pink-400 font-medium mb-2">Design Head</p>
                    </div>
                    <div class="team-member-card card p-6 rounded-xl shadow-lg text-center group cursor-pointer"
                        data-name="Kunal Thakur" data-title="Content Head" data-img="assets\Content.jpeg"
                        data-bio="Kunal is responsible for crafting compelling narratives and engaging content that tells the E-Cell's story and promotes its initiatives.">
                        <img src="assets\Content.jpeg" alt="Kunal Thakur"
                            class="w-36 h-35 mx-auto rounded-full object-cover mb-4 ring-4 ring-pink-400 group-hover:ring-purple-400 transition-colors">
                        <h3 class="text-xl font-bold text-white">Kunal Thakur</h3>
                        <p class="text-purple-400 font-medium mb-2">Content Head</p>
                    </div>
                    <div class="team-member-card card p-6 rounded-xl shadow-lg text-center group cursor-pointer"
                        data-name="Shivansh Shukla" data-title="Finance Head" data-img="assets\Finance.jpeg"
                        data-bio="Shivansh manages the E-Cell's finances, ensuring resources are allocated effectively to support our events, workshops, and startup incubation programs.">
                        <img src="assets\Finance.jpeg" alt="Shivansh Shukla"
                            class="w-36 h-35 mx-auto rounded-full object-cover mb-4 ring-4 ring-purple-400 group-hover:ring-pink-400 transition-colors">
                        <h3 class="text-xl font-bold text-white">Shivansh Shukla</h3>
                        <p class="text-pink-400 font-medium mb-2">Finance Head</p>
                    </div>
                </div>
            </div>
        </section>

        <section id="events" class="py-20 bg-slate-900">
            <div class="container mx-auto px-4 text-center">
                <h2 class="text-3xl sm:text-4xl font-bold mb-4 text-white">Events & Activities</h2>
                <p class="text-gray-400 max-w-2xl mx-auto text-lg mb-12">
                    Join our dynamic events designed to accelerate your entrepreneurial journey.
                </p>
                <div class="grid gap-8 lg:grid-cols-2">
                    <div class="lg:col-span-2 text-left">
                        <h3 class="text-2xl font-bold mb-6 text-purple-400">Upcoming</h3>
                        <div class="grid sm:grid-cols-2 gap-6">
                            <div class="card p-6 rounded-xl shadow-lg group">
                                <h4 class="text-xl font-bold mb-2 text-white">📢 Eureka 2025 Registrations Now Open!
                                </h4>
                                <p class="text-gray-400 mb-4">E-Cell APSIT's flagship startup and innovation event to
                                    empower student founders, creators, and problem-solvers.</p>
                                <ul class="text-sm space-y-1 text-gray-500">
                                    <li><strong>Last Date to Register:</strong> 14th August 2025</li>
                                    <li><strong>Location:</strong> APSIT - 008 Seminar Hall</li>
                                    <li><strong>Tracks Include:</strong> AI, Sustainability, Startup Ideas, Business
                                        Plans & more</li>
                                    <li>
                                        <strong>Contacts:</strong><br>
                                        Surili Ghosh: Technical Head, E-Cell APSIT 📞 +91 91525 89259<br>
                                        Shivansh Shukla: Finance Head, E-Cell APSIT 📞 +91 77189 92512
                                    </li>
                                </ul>
                                <div class="mt-6 flex flex-wrap gap-4">
                                    <a href="https://docs.google.com/forms/d/e/1FAIpQLSeokmhjy4u8h3NzvRmNwoGF_0To9F1ftIhu96qqw2fQBGZJVw/viewform?usp=header"
                                        target="_blank" rel="noopener noreferrer"
                                        class="neon-button text-sm flex-grow">Register Now</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            </div>
        </section>

        <section id="contact" class="py-20 bg-slate-800 text-white">
            <div class="container mx-auto px-4 text-center">
                <h2 class="text-3xl sm:text-4xl font-bold mb-4 text-white">Let's Connect</h2>
                <p class="text-gray-400 max-w-3xl mx-auto text-lg mb-12">
                    Ready to start your entrepreneurial journey? We're here to help you succeed.
                </p>
                <link rel="stylesheet"
                    href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
                <div class="flex flex-col sm:flex-row justify-center items-center gap-8">
                    <div class="text-center card p-6 rounded-xl shadow-lg">
                        <i class="bi bi-instagram text-4xl mx-auto mb-2 text-pink-400"></i>
                        <p class="text-gray-300 font-semibold">Instagram</p>
                        <a href="https://www.instagram.com/ecell_apsit?igsh=NWY3Mth2eGYwd2Jp" target="_blank"
                            class="text-purple-400 hover:underline">ecell_apsit</a>
                    </div>
                    <div class="text-center card p-6 rounded-xl shadow-lg">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 mx-auto mb-2 text-pink-400" fill="none"
                            viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.75 21 3 14.25 3 6V5z" />
                        </svg>
                        <p class="text-gray-300 font-semibold">Phone</p>
                        <a href="tel:+917718992512" class="text-purple-400 hover:underline">+91 77189 92512</a>
                    </div>
                    <div class="text-center card p-6 rounded-xl shadow-lg">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 mx-auto mb-2 text-pink-400" fill="none"
                            viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                        <p class="text-gray-300 font-semibold">Location</p>
                        <p class="text-gray-400">A.P. Shah Institute of Technology, Thane</p>
                    </div>
                </div>

            </div>
        </section>
    </main>

    <footer class="bg-slate-900 text-gray-400 py-10">
        <div class="container mx-auto px-4 text-center">
            <div class="mb-4">
                <a href="#" class="text-xl font-bold text-white">E-Cell APSIT</a>
            </div>
            <p class="text-sm max-w-2xl mx-auto">
                Fostering innovation and entrepreneurship at A.P. Shah Institute of Technology. Join us in building the
                next generation of successful entrepreneurs and changemakers.
            </p>
            <div class="mt-8">
                <p class="text-sm">Made with <span class="text-pink-400">❤</span> by the E-Cell Team</p>
                <p class="text-sm mt-2">© 2025 E-Cell APSIT. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Team Member Modal -->
    <div id="team-modal" class="modal">
        <div class="modal-content text-center">
            <span class="close-button">&times;</span>
            <img id="modal-img" src="" alt="Team Member"
                class="w-48 h-48 mx-auto rounded-full object-cover mb-4 ring-4 ring-purple-400">
            <h3 id="modal-name" class="text-3xl font-bold text-white"></h3>
            <p id="modal-title" class="text-xl text-pink-400 font-medium mb-4"></p>
            <p id="modal-bio" class="text-gray-400 text-left"></p>
        </div>
    </div>

    <!-- Event Promo Modal -->
    <div id="promo-modal" class="modal">
        <div class="modal-content">
            <span class="close-button" id="promo-close-button">&times;</span>
            <h3 class="text-2xl font-bold text-white mb-4">Social Media Post</h3>
            <div id="promo-loading" class="text-gray-400 text-center flex items-center justify-center gap-2 hidden">
                <span class="spinner"></span> Generating post...
            </div>
            <div id="promo-content-wrapper">
                <textarea id="promo-content" readonly
                    class="w-full p-4 h-48 bg-slate-900 text-white rounded-lg border border-slate-700 whitespace-pre-line"></textarea>
                <button id="copy-promo-button" class="neon-button mt-4 w-full text-sm">Copy to Clipboard</button>
            </div>
        </div>
    </div>

    <script>
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const targetId = this.getAttribute('href');
                const targetElement = document.querySelector(targetId);
                if (targetElement) {
                    targetElement.scrollIntoView({ behavior: 'smooth' });
                }
                // Also close mobile menu if open
                const mobileMenu = document.getElementById('mobile-menu');
                if (!mobileMenu.classList.contains('-translate-x-full')) {
                    mobileMenu.classList.add('-translate-x-full');
                }
            });
        });

        window.addEventListener('scroll', () => {
            const header = document.getElementById('header');
            if (window.scrollY > 50) {
                header.classList.add('header-scrolled');
            } else {
                header.classList.remove('header-scrolled');
            }
        });

        const mobileMenuButton = document.getElementById('mobile-menu-button');
        const closeMenuButton = document.getElementById('close-menu-button');
        const mobileMenu = document.getElementById('mobile-menu');
        mobileMenuButton.addEventListener('click', () => mobileMenu.classList.remove('-translate-x-full'));
        closeMenuButton.addEventListener('click', () => mobileMenu.classList.add('-translate-x-full'));

        const counters = document.querySelectorAll('[data-target]');
        const animateCounters = () => {
            counters.forEach(counter => {
                const target = +counter.getAttribute('data-target');
                let count = 0;
                const updateCount = () => {
                    const increment = target / 200;
                    if (count < target) {
                        count += increment;
                        counter.innerText = Math.ceil(count);
                        setTimeout(updateCount, 1);
                    } else {
                        counter.innerText = target;
                    }
                };
                updateCount();
            });
        };
        const impactSection = document.getElementById('impact');
        const observer = new IntersectionObserver((entries, obs) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    animateCounters();
                    obs.disconnect();
                }
            });
        }, { threshold: 0.5 });
        observer.observe(impactSection);

        // --- Team Modal Functionality ---
        const teamModal = document.getElementById('team-modal');
        const modalImg = document.getElementById('modal-img');
        const modalName = document.getElementById('modal-name');
        const modalTitle = document.getElementById('modal-title');
        const modalBio = document.getElementById('modal-bio');
        const teamCloseBtn = teamModal.querySelector('.close-button');

        document.querySelectorAll('.team-member-card').forEach(card => {
            card.addEventListener('click', () => {
                modalImg.src = card.dataset.img;
                modalName.textContent = card.dataset.name;
                modalTitle.textContent = card.dataset.title;
                modalBio.textContent = card.dataset.bio;
                teamModal.style.display = 'block';
            });
        });

        const closeTeamModal = () => teamModal.style.display = 'none';
        teamCloseBtn.addEventListener('click', closeTeamModal);

        // --- Gemini API Functionality ---
        async function callGeminiAPI(prompt) {
            let retryCount = 0;
            const maxRetries = 5;
            while (retryCount < maxRetries) {
                try {
                    const payload = {
                        contents: [{
                            role: "user",
                            parts: [{
                                text: prompt
                            }]
                        }]
                    };
                    const apiKey = ""; // API key is handled by the environment
                    const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent?key=${apiKey}`;
                    const response = await fetch(apiUrl, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(payload)
                    });
                    if (!response.ok) throw new Error(`API error: ${response.status}`);
                    const result = await response.json();
                    if (result.candidates?.[0]?.content?.parts?.[0]?.text) {
                        return result.candidates[0].content.parts[0].text;
                    } else {
                        throw new Error('Unexpected API response structure.');
                    }
                } catch (error) {
                    console.error(`Attempt ${retryCount + 1} failed:`, error);
                    retryCount++;
                    if (retryCount >= maxRetries) {
                        return "Sorry, I couldn't generate a response. Please try again later.";
                    }
                    await new Promise(res => setTimeout(res, Math.pow(2, retryCount) * 1000));
                }
            }
        }

        // --- Founder's Toolkit: Innovation Spark ---
        const ideaInput = document.getElementById('idea-input');
        const generateIdeaButton = document.getElementById('generate-idea-button');
        const ideaResultDiv = document.getElementById('idea-result');
        const ideaLoading = document.getElementById('idea-loading');
        const ideaContent = document.getElementById('idea-content');

        generateIdeaButton.addEventListener('click', async () => {
            const interests = ideaInput.value.trim();
            if (!interests) {
                ideaContent.textContent = "Please enter your interests or an idea to get started.";
                ideaResultDiv.classList.remove('hidden');
                return;
            }
            ideaResultDiv.classList.remove('hidden');
            ideaLoading.classList.remove('hidden');
            ideaContent.classList.add('hidden');
            generateIdeaButton.disabled = true;

            const prompt = `You are a creative idea generator for a college entrepreneurship cell. Based on the user's input: "${interests}", provide a creative startup idea. Format the response with Markdown headings.
            **Idea Name:**
            **Tagline:**
            **Problem:**
            **Solution:**
            **Target Audience:**
            **Unique Value Proposition:**`;

            const generatedIdea = await callGeminiAPI(prompt);
            ideaContent.innerHTML = generatedIdea.replace(/\*\*(.*?)\*\*/g, '<strong class="text-purple-300">$1</strong>');
            ideaLoading.classList.add('hidden');
            ideaContent.classList.remove('hidden');
            generateIdeaButton.disabled = false;
        });

        // --- Founder's Toolkit: Pitch Deck Generator ---
        const pitchInput = document.getElementById('pitch-input');
        const generatePitchButton = document.getElementById('generate-pitch-button');
        const pitchResultDiv = document.getElementById('pitch-result');
        const pitchLoading = document.getElementById('pitch-loading');
        const pitchContent = document.getElementById('pitch-content');

        generatePitchButton.addEventListener('click', async () => {
            const idea = pitchInput.value.trim();
            if (!idea) {
                pitchContent.textContent = "Please enter your startup idea to generate a pitch outline.";
                pitchResultDiv.classList.remove('hidden');
                return;
            }
            pitchResultDiv.classList.remove('hidden');
            pitchLoading.classList.remove('hidden');
            pitchContent.classList.add('hidden');
            generatePitchButton.disabled = true;

            const prompt = `Act as a startup mentor. Create a standard 10-slide pitch deck outline for the following startup idea: "${idea}". For each slide, provide a title and 3-4 bullet points of what to include. Format the response using Markdown.
            1. **Title Slide:** (Company Name, Tagline, Contact Info)
            2. **Problem:** (What is the pain point you're solving?)
            3. **Solution:** (How does your product/service solve it?)
            4. **Product/Service:** (How does it work? Demo/Screenshots)
            5. **Market Size:** (How big is the opportunity? TAM, SAM, SOM)
            6. **Business Model:** (How do you make money?)
            7. **Go-to-Market Strategy:** (How will you reach customers?)
            8. **Competition:** (Who are your competitors and what's your advantage?)
            9. **Team:** (Why are you the right people to build this?)
            10. **The Ask:** (How much funding are you seeking and for what?)`;

            const generatedPitch = await callGeminiAPI(prompt);
            pitchContent.innerHTML = generatedPitch.replace(/\*\*(.*?)\*\*/g, '<strong class="text-pink-300">$1</strong>').replace(/(\d+\.)/g, '<br><strong class="text-pink-400">$1</strong>');
            pitchLoading.classList.add('hidden');
            pitchContent.classList.remove('hidden');
            generatePitchButton.disabled = false;
        });

        // --- Event Promo Modal Functionality ---
        const promoModal = document.getElementById('promo-modal');
        const promoLoading = document.getElementById('promo-loading');
        const promoContentWrapper = document.getElementById('promo-content-wrapper');
        const promoContent = document.getElementById('promo-content');
        const promoCloseBtn = document.getElementById('promo-close-button');
        const copyPromoBtn = document.getElementById('copy-promo-button');

        document.querySelectorAll('.promo-button').forEach(button => {
            button.addEventListener('click', async () => {
                promoModal.style.display = 'block';
                promoLoading.classList.remove('hidden');
                promoContentWrapper.classList.add('hidden');

                const title = button.dataset.eventTitle;
                const details = button.dataset.eventDetails;

                const prompt = `You are a social media manager for a college E-Cell. Create a short, exciting, and professional promotional post for an upcoming event.
                Event Title: ${title}
                Details: ${details}
                The post should be engaging for students, mention the E-Cell, and include relevant hashtags like #Entrepreneurship #Startup #CollegeEvent #APSIT #EcellAPSIT.`;

                const generatedPost = await callGeminiAPI(prompt);
                promoContent.value = generatedPost;
                promoLoading.classList.add('hidden');
                promoContentWrapper.classList.remove('hidden');
                copyPromoBtn.textContent = 'Copy to Clipboard';
            });
        });

        copyPromoBtn.addEventListener('click', () => {
            promoContent.select();
            promoContent.setSelectionRange(0, 99999); // For mobile devices
            try {
                document.execCommand('copy');
                copyPromoBtn.textContent = 'Copied!';
            } catch (err) {
                console.error('Failed to copy text: ', err);
                copyPromoBtn.textContent = 'Copy Failed';
            }
        });

        const closePromoModal = () => promoModal.style.display = 'none';
        promoCloseBtn.addEventListener('click', closePromoModal);

        window.addEventListener('click', (event) => {
            if (event.target == teamModal) closeTeamModal();
            if (event.target == promoModal) closePromoModal();
        });

        window.addEventListener('keydown', (event) => {
            if (event.key === 'Escape') {
                closeTeamModal();
                closePromoModal();
            }
        });
    </script>
</body>

</html>